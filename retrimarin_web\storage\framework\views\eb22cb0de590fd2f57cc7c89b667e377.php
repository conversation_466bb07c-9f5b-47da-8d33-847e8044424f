<?php $__env->startSection('title', 'Dashboard - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Header Welcome -->
    <div class="card bg-light-primary shadow-none position-relative overflow-hidden mb-4">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Selamat Datang di Sistem Manajemen Kapal</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a class="text-muted text-decoration-none" href="<?php echo e(route('dashboard')); ?>">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">Overview</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 zoom-in bg-light-primary shadow-none">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div
                            class="round-40 rounded-circle text-white d-flex align-items-center justify-content-center bg-primary">
                            <i class="ti ti-ship fs-6"></i>
                        </div>
                        <div class="ms-3">
                            <div class="d-flex align-items-center">
                                <h3 class="fw-semibold text-dark mb-0"><?php echo e($totalKapal); ?></h3>
                            </div>
                            <p class="mb-0 text-dark">Total Kapal</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 zoom-in bg-light-success shadow-none">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div
                            class="round-40 rounded-circle text-white d-flex align-items-center justify-content-center bg-success">
                            <i class="ti ti-check fs-6"></i>
                        </div>
                        <div class="ms-3">
                            <div class="d-flex align-items-center">
                                <h3 class="fw-semibold text-dark mb-0"><?php echo e($kapalAktif); ?></h3>
                            </div>
                            <p class="mb-0 text-dark">Kapal Aktif</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 zoom-in bg-light-warning shadow-none">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div
                            class="round-40 rounded-circle text-white d-flex align-items-center justify-content-center bg-warning">
                            <i class="ti ti-tools fs-6"></i>
                        </div>
                        <div class="ms-3">
                            <div class="d-flex align-items-center">
                                <h3 class="fw-semibold text-dark mb-0"><?php echo e($kapalMaintenance); ?></h3>
                            </div>
                            <p class="mb-0 text-dark">Maintenance</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 zoom-in bg-light-info shadow-none">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div
                            class="round-40 rounded-circle text-white d-flex align-items-center justify-content-center bg-info">
                            <i class="ti ti-map-pin fs-6"></i>
                        </div>
                        <div class="ms-3">
                            <div class="d-flex align-items-center">
                                <h3 class="fw-semibold text-dark mb-0"><?php echo e($wilayahTerkonfigurasi); ?></h3>
                            </div>
                            <p class="mb-0 text-dark">Wilayah Teritorial</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Retribusi Statistics -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 zoom-in bg-light-primary shadow-none">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div
                            class="round-40 rounded-circle text-white d-flex align-items-center justify-content-center bg-primary">
                            <i class="ti ti-file-text fs-6"></i>
                        </div>
                        <div class="ms-3">
                            <div class="d-flex align-items-center">
                                <h3 class="fw-semibold text-dark mb-0"><?php echo e(number_format($totalRetribusi)); ?></h3>
                            </div>
                            <p class="mb-0 text-dark">Total Retribusi</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 zoom-in bg-light-success shadow-none">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div
                            class="round-40 rounded-circle text-white d-flex align-items-center justify-content-center bg-success">
                            <i class="ti ti-currency-dollar fs-6"></i>
                        </div>
                        <div class="ms-3">
                            <div class="d-flex align-items-center">
                                <h3 class="fw-semibold text-dark mb-0"><?php
                $amount = $totalPendapatan;
                if ($amount >= 1000000000) {
                    echo number_format($amount / 1000000000, 1) . ' Milyar';
                } elseif ($amount >= 1000000) {
                    echo number_format($amount / 1000000, 1) . ' Juta';
                } elseif ($amount >= 1000) {
                    echo number_format($amount / 1000, 0) . ' Ribu';
                } else {
                    echo number_format($amount, 0);
                }
            ?></h3>
                            </div>
                            <p class="mb-0 text-dark">Total Pendapatan</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 zoom-in bg-light-info shadow-none">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div
                            class="round-40 rounded-circle text-white d-flex align-items-center justify-content-center bg-info">
                            <i class="ti ti-calendar fs-6"></i>
                        </div>
                        <div class="ms-3">
                            <div class="d-flex align-items-center">
                                <h3 class="fw-semibold text-dark mb-0"><?php echo e($retribusiHariIni); ?></h3>
                            </div>
                            <p class="mb-0 text-dark">Retribusi Hari Ini</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 zoom-in bg-light-warning shadow-none">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div
                            class="round-40 rounded-circle text-white d-flex align-items-center justify-content-center bg-warning">
                            <i class="ti ti-cash fs-6"></i>
                        </div>
                        <div class="ms-3">
                            <div class="d-flex align-items-center">
                                <h3 class="fw-semibold text-dark mb-0"><?php
                $amount = $pendapatanHariIni;
                if ($amount >= 1000000000) {
                    echo number_format($amount / 1000000000, 1) . ' Milyar';
                } elseif ($amount >= 1000000) {
                    echo number_format($amount / 1000000, 1) . ' Juta';
                } elseif ($amount >= 1000) {
                    echo number_format($amount / 1000, 0) . ' Ribu';
                } else {
                    echo number_format($amount, 0);
                }
            ?></h3>
                            </div>
                            <p class="mb-0 text-dark">Pendapatan Hari Ini</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title fw-semibold mb-4">Aksi Cepat</h5>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo e(route('kapal.create')); ?>" class="btn btn-primary w-100 py-3">
                                <i class="ti ti-plus fs-5 mb-2 d-block"></i>
                                <span class="fw-semibold">Tambah Kapal Baru</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo e(route('kapal.index')); ?>" class="btn btn-outline-primary w-100 py-3">
                                <i class="ti ti-list fs-5 mb-2 d-block"></i>
                                <span class="fw-semibold">Lihat Semua Kapal</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo e(route('tracking.index')); ?>" class="btn btn-outline-success w-100 py-3">
                                <i class="ti ti-activity fs-5 mb-2 d-block"></i>
                                <span class="fw-semibold">Data Tracking</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo e(route('wilayah.index')); ?>" class="btn btn-outline-info w-100 py-3">
                                <i class="ti ti-map-pin fs-5 mb-2 d-block"></i>
                                <span class="fw-semibold">Batas Wilayah</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card bg-light-success">
                <div class="card-body">
                    <h5 class="card-title fw-semibold mb-4">Status Sistem</h5>
                    <div class="d-flex align-items-center mb-3">
                        <div
                            class="round-30 rounded-circle bg-success text-white d-flex align-items-center justify-content-center me-3">
                            <i class="ti ti-check fs-5"></i>
                        </div>
                        <div>
                            <h6 class="fw-semibold mb-0">Sistem Online</h6>
                            <p class="mb-0 text-muted">Semua layanan berjalan normal</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <div
                            class="round-30 rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3">
                            <i class="ti ti-database fs-5"></i>
                        </div>
                        <div>
                            <h6 class="fw-semibold mb-0">Database Aktif</h6>
                            <p class="mb-0 text-muted">Koneksi database stabil</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <div
                            class="round-30 rounded-circle bg-info text-white d-flex align-items-center justify-content-center me-3">
                            <i class="ti ti-shield-check fs-5"></i>
                        </div>
                        <div>
                            <h6 class="fw-semibold mb-0">Keamanan Aktif</h6>
                            <p class="mb-0 text-muted">Sistem autentikasi berjalan</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Retribusi -->
    <?php if($recentRetribusi->count() > 0): ?>
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-sm-flex d-block align-items-center justify-content-between mb-9">
                            <div class="mb-3 mb-sm-0">
                                <h5 class="card-title fw-semibold">Retribusi Terbaru</h5>
                                <p class="card-subtitle mb-0">5 retribusi terakhir yang dibuat</p>
                            </div>
                            <div>
                                <a href="<?php echo e(route('admin.retribusi.index')); ?>" class="btn btn-primary">
                                    <i class="ti ti-eye me-1"></i>Lihat Semua
                                </a>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nomor</th>
                                        <th>Petugas</th>
                                        <th>Kapal</th>
                                        <th>Jenis Pembayar</th>
                                        <th>Total</th>
                                        <th>Status</th>
                                        <th>Tanggal</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $recentRetribusi; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $retribusi): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($retribusi->nomor_retribusi); ?></td>
                                            <td><?php echo e($retribusi->petugas->nama_petugas); ?></td>
                                            <td><?php echo e($retribusi->kapal->nama_kapal); ?></td>
                                            <td>
                                                <span
                                                    class="badge bg-<?php echo e($retribusi->jenis_pembayar == 'pribadi' ? 'info' : 'primary'); ?>">
                                                    <?php echo e($retribusi->jenis_pembayar_label); ?>

                                                </span>
                                            </td>
                                            <td><?php echo e($retribusi->formatted_total_harga); ?></td>
                                            <td>
                                                <?php if($retribusi->status_pembayaran == 'pending'): ?>
                                                    <span class="badge bg-warning">Pending</span>
                                                <?php elseif($retribusi->status_pembayaran == 'lunas'): ?>
                                                    <span class="badge bg-success">Lunas</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Batal</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($retribusi->tanggal_transaksi->format('d/m/Y H:i')); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Management Features -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title fw-semibold mb-4">Fitur Manajemen</h5>
                    <div class="row">
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card border border-primary">
                                <div class="card-body text-center">
                                    <div class="rounded-circle bg-light-primary text-primary d-flex align-items-center justify-content-center mx-auto mb-3"
                                        style="width: 80px; height: 80px;">
                                        <i class="ti ti-ship fs-8"></i>
                                    </div>
                                    <h6 class="fw-semibold">Manajemen Kapal</h6>
                                    <p class="text-muted mb-3">Kelola data kapal, operator, dan informasi teknis lengkap
                                    </p>
                                    <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                    <div class="mt-3">
                                        <a href="<?php echo e(route('kapal.index')); ?>" class="btn btn-primary btn-sm">Kelola
                                            Kapal</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card border border-info">
                                <div class="card-body text-center">
                                    <div class="rounded-circle bg-light-info text-info d-flex align-items-center justify-content-center mx-auto mb-3"
                                        style="width: 80px; height: 80px;">
                                        <i class="ti ti-map-pin fs-8"></i>
                                    </div>
                                    <h6 class="fw-semibold">Batas Wilayah</h6>
                                    <p class="text-muted mb-3">Konfigurasi dan monitoring batas wilayah teritorial</p>
                                    <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                    <div class="mt-3">
                                        <a href="<?php echo e(route('wilayah.index')); ?>" class="btn btn-info btn-sm">Kelola
                                            Wilayah</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card border border-success">
                                <div class="card-body text-center">
                                    <div class="rounded-circle bg-light-success text-success d-flex align-items-center justify-content-center mx-auto mb-3"
                                        style="width: 80px; height: 80px;">
                                        <i class="ti ti-map fs-8"></i>
                                    </div>
                                    <h6 class="fw-semibold">Monitoring Real-time</h6>
                                    <p class="text-muted mb-3">Pantau status dan lokasi kapal secara real-time</p>
                                    <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                    <div class="mt-3">
                                        <a href="<?php echo e(route('tracking.map')); ?>" class="btn btn-success btn-sm">Lihat
                                            Peta</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp-8.2-new\htdocs\kapal\retrimarin_web\resources\views/dashboard/index.blade.php ENDPATH**/ ?>