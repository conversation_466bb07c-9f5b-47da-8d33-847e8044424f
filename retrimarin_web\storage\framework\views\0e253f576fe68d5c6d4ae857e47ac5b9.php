<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">

<head>
    <!--  Title -->
    <title><?php echo e(config('app.name', 'Laravel')); ?> - Login</title>
    <!--  Required Meta Tag -->
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="handheldfriendly" content="true" />
    <meta name="MobileOptimized" content="width" />
    <meta name="description" content="<?php echo e(config('app.name', 'Laravel')); ?>" />
    <meta name="author" content="" />
    <meta name="keywords" content="<?php echo e(config('app.name', 'Laravel')); ?>" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--  Favicon -->
    <link rel="shortcut icon" type="image/png" href="<?php echo e(asset('favicon.ico')); ?>" />
    <!-- Core Css -->
    <link rel="stylesheet" href="<?php echo e(asset('package/dist/css/style.min.css')); ?>" />
</head>

<body>
    <!-- Preloader -->
    <div class="preloader">
        <img src="<?php echo e(asset('package/dist/images/logos/favicon.png')); ?>" alt="loader" class="lds-ripple img-fluid" />
    </div>
    <!--  Body Wrapper -->
    <div class="page-wrapper" id="main-wrapper" data-layout="vertical" data-sidebartype="full"
        data-sidebar-position="fixed" data-header-position="fixed">
        <div
            class="position-relative overflow-hidden radial-gradient min-vh-100 d-flex align-items-center justify-content-center">
            <div class="d-flex align-items-center justify-content-center w-100">
                <div class="row justify-content-center w-100">
                    <div class="col-md-8 col-lg-6 col-xxl-3">
                        <div class="card mb-0">
                            <div class="card-body">
                                <a href="<?php echo e(url('/')); ?>"
                                    class="text-nowrap logo-img text-center d-block mb-5 w-100">
                                    <div class="d-flex flex-column align-items-center justify-content-center">
                                        <div class="mb-2">
                                            <img src="<?php echo e(asset('package/dist/images/logo-batola.png')); ?>" width="50"
                                                alt="">
                                        </div>

                                        <div class="ms-3">
                                            
                                            <p class="text-dark fw-bold fs-5 mb-0">Command Centre Batola</p>
                                        </div>
                                    </div>
                                </a>
                                <form method="POST" action="<?php echo e(route('login')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Username</label>
                                        <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                            id="email" name="email" value="<?php echo e(old('email')); ?>" required
                                            autofocus autocomplete="username">
                                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="mb-4">
                                        <label for="password" class="form-label">Password</label>
                                        <input type="password"
                                            class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="password"
                                            name="password" required autocomplete="current-password">
                                        <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                    <div class="d-flex align-items-center justify-content-between mb-4">
                                        <div class="form-check">
                                            <input class="form-check-input primary" type="checkbox" value=""
                                                id="remember_me" name="remember">
                                            <label class="form-check-label text-dark" for="remember_me">
                                                Remeber this Device
                                            </label>
                                        </div>
                                        <?php if(Route::has('password.request')): ?>
                                            <a class="text-primary fw-medium"
                                                href="<?php echo e(route('password.request')); ?>">Forgot Password ?</a>
                                        <?php endif; ?>
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100 py-8 mb-4 rounded-2">Sign
                                        In</button>
                                    <div class="d-flex align-items-center justify-content-center">
                                        <p class="fs-4 mb-0 fw-medium">New to <?php echo e(config('app.name', 'Laravel')); ?>?</p>
                                        <?php if(Route::has('register')): ?>
                                            <a class="text-primary fw-medium ms-2"
                                                href="<?php echo e(route('register')); ?>">Create an account</a>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--  Import Js Files -->
    <script src="<?php echo e(asset('package/dist/libs/jquery/dist/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/libs/simplebar/dist/simplebar.min.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/libs/bootstrap/dist/js/bootstrap.bundle.min.js')); ?>"></script>
    <!--  core files -->
    <script src="<?php echo e(asset('package/dist/js/app.min.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/js/app.init.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/js/app-style-switcher.js')); ?>"></script>
    <script src="<?php echo e(asset('package/dist/js/sidebarmenu.js')); ?>"></script>

    <script src="<?php echo e(asset('package/dist/js/custom.js')); ?>"></script>
</body>

</html>
<?php /**PATH C:\xampp-8.2-new\htdocs\kapal\retrimarin_web\resources\views/auth/login.blade.php ENDPATH**/ ?>