<?php $__env->startSection('title', 'Dashboard Petugas - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Header -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Dashboard Petugas</h4>
                    <p class="mb-0">Selamat datang, <?php echo e($petugas->nama_petugas); ?></p>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 zoom-in bg-light-primary shadow-none">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div
                            class="round-40 rounded-circle text-white d-flex align-items-center justify-content-center bg-primary">
                            <i class="ti ti-file-text fs-6"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="mb-0 fs-4">Total Retribusi</h6>
                            <h4 class="fw-semibold mb-0"><?php echo e($totalRetribusi); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 zoom-in bg-light-warning shadow-none">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div
                            class="round-40 rounded-circle text-white d-flex align-items-center justify-content-center bg-warning">
                            <i class="ti ti-clock fs-6"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="mb-0 fs-4">Pending</h6>
                            <h4 class="fw-semibold mb-0"><?php echo e($retribusiPending); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 zoom-in bg-light-success shadow-none">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div
                            class="round-40 rounded-circle text-white d-flex align-items-center justify-content-center bg-success">
                            <i class="ti ti-check fs-6"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="mb-0 fs-4">Lunas</h6>
                            <h4 class="fw-semibold mb-0"><?php echo e($retribusiLunas); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card border-0 zoom-in bg-light-info shadow-none">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div
                            class="round-40 rounded-circle text-white d-flex align-items-center justify-content-center bg-info">
                            <i class="ti ti-currency-dollar fs-6"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="mb-0 fs-4">Total Pendapatan</h6>
                            <h4 class="fw-semibold mb-0">Rp <?php echo e(number_format($totalPendapatan, 0, ',', '.')); ?></h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-sm-flex d-block align-items-center justify-content-between mb-9">
                        <div class="mb-3 mb-sm-0">
                            <h5 class="card-title fw-semibold">Aksi Cepat</h5>
                            <p class="card-subtitle mb-0">Akses fitur utama dengan cepat</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <a href="<?php echo e(route('petugas.retribusi.create')); ?>" class="btn btn-primary w-100 mb-3">
                                <i class="ti ti-plus me-2"></i>Buat Retribusi Baru
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="<?php echo e(route('petugas.retribusi.index')); ?>" class="btn btn-outline-primary w-100 mb-3">
                                <i class="ti ti-list me-2"></i>Lihat Semua Retribusi
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="<?php echo e(route('petugas.retribusi.index', ['status' => 'pending'])); ?>"
                                class="btn btn-outline-warning w-100 mb-3">
                                <i class="ti ti-clock me-2"></i>Retribusi Pending
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Retribusi -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-sm-flex d-block align-items-center justify-content-between mb-9">
                        <div class="mb-3 mb-sm-0">
                            <h5 class="card-title fw-semibold">Retribusi Terbaru</h5>
                            <p class="card-subtitle mb-0">10 retribusi terakhir yang dibuat</p>
                        </div>
                        <div>
                            <a href="<?php echo e(route('petugas.retribusi.index')); ?>" class="btn btn-outline-primary">
                                Lihat Semua
                            </a>
                        </div>
                    </div>

                    <?php if($recentRetribusi->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>No. Retribusi</th>
                                        <th>Kapal</th>
                                        <th>Jenis Tarif</th>
                                        <th>Total</th>
                                        <th>Status</th>
                                        <th>Tanggal</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $recentRetribusi; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $retribusi): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($retribusi->nomor_retribusi); ?></td>
                                            <td><?php echo e($retribusi->kapal->nama_kapal); ?></td>
                                            <td><?php echo e($retribusi->tarif->jenis_tarif_label); ?></td>
                                            <td><?php echo e($retribusi->formatted_total_harga); ?></td>
                                            <td>
                                                <?php if($retribusi->status_pembayaran == 'pending'): ?>
                                                    <span class="badge bg-warning">Pending</span>
                                                <?php elseif($retribusi->status_pembayaran == 'lunas'): ?>
                                                    <span class="badge bg-success">Lunas</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Batal</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($retribusi->tanggal_transaksi->format('d/m/Y H:i')); ?></td>
                                            <td>
                                                <a href="<?php echo e(route('petugas.retribusi.show', $retribusi->id)); ?>"
                                                    class="btn btn-sm btn-outline-primary" title="Lihat Detail Retribusi"
                                                    data-bs-toggle="tooltip">
                                                    <i class="ti ti-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <img src="<?php echo e(asset('package/dist/images/backgrounds/empty-shopping-cart.gif')); ?>"
                                alt="" class="img-fluid mb-4" width="200">
                            <h5 class="fw-semibold">Belum ada retribusi</h5>
                            <p class="text-muted">Mulai buat retribusi pertama Anda</p>
                            <a href="<?php echo e(route('petugas.retribusi.create')); ?>" class="btn btn-primary">
                                <i class="ti ti-plus me-2"></i>Buat Retribusi
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('petugas.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp-8.2-new\htdocs\kapal\retrimarin_web\resources\views/petugas/dashboard.blade.php ENDPATH**/ ?>