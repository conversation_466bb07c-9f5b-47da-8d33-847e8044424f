<?php $__env->startSection('title', 'Data Petugas - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Header Fitur -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Data Petugas</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted" href="<?php echo e(route('dashboard')); ?>">Dashboard</a>
                            </li>
                            <li class="breadcrumb-item" aria-current="page">Data Petugas</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-sm-flex d-block align-items-center justify-content-between mb-9">
                        <div class="mb-3 mb-sm-0">
                            <h5 class="card-title fw-semibold">Daftar Petugas</h5>
                            <p class="card-subtitle mb-0">Kelola data petugas yang dapat mengakses sistem retribusi</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="<?php echo e(route('petugas.create')); ?>" class="btn btn-primary">
                                <i class="ti ti-plus me-2"></i>Tambah Petugas
                            </a>
                        </div>
                    </div>

                    <?php if($petugas->count() > 0): ?>
                        <div class="table-responsive">
                            <table id="petugas_datatable" class="table border table-striped table-bordered text-nowrap">
                                <thead>
                                    <tr>
                                        <th>Nama Petugas</th>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Jabatan</th>
                                        <th>Status</th>
                                        <th>Last Login</th>
                                        <th style="width: 120px;">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $petugas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $p): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <h6 class="fw-semibold mb-0"><?php echo e($p->nama_petugas); ?></h6>
                                                <?php if($p->telepon): ?>
                                                    <small class="text-muted"><?php echo e($p->telepon); ?></small>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($p->username); ?></td>
                                            <td><?php echo e($p->email); ?></td>
                                            <td><?php echo e($p->jabatan ?? '-'); ?></td>
                                            <td>
                                                <?php if($p->status == 'active'): ?>
                                                    <span class="badge bg-success rounded-3 fw-semibold">Aktif</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger rounded-3 fw-semibold">Tidak Aktif</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if($p->last_login_at): ?>
                                                    <?php echo e($p->last_login_at->format('d/m/Y H:i')); ?>

                                                <?php else: ?>
                                                    <span class="text-muted">Belum pernah login</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="d-flex gap-1">
                                                    <a href="<?php echo e(route('petugas.show', $p->id)); ?>"
                                                        class="btn btn-sm btn-outline-info" title="Detail">
                                                        <i class="ti ti-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('petugas.edit', $p->id)); ?>"
                                                        class="btn btn-sm btn-outline-warning" title="Edit">
                                                        <i class="ti ti-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('petugas.destroy', $p->id)); ?>" method="POST"
                                                        class="d-inline"
                                                        onsubmit="return confirm('Apakah Anda yakin ingin menghapus petugas ini?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-outline-danger"
                                                            title="Hapus">
                                                            <i class="ti ti-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="d-flex flex-column align-items-center justify-content-center py-5">
                            <img src="<?php echo e(asset('package/dist/images/backgrounds/empty-shopping-cart.gif')); ?>"
                                alt="" class="img-fluid mb-4" width="200">
                            <h5 class="fw-semibold">Belum ada data petugas</h5>
                            <p class="text-muted text-center">Silakan tambah petugas baru untuk mulai mengelola data
                                retribusi</p>
                            <a href="<?php echo e(route('petugas.create')); ?>" class="btn btn-primary">
                                <i class="ti ti-plus me-2"></i>Tambah Petugas Pertama
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
    <?php if($petugas->count() > 0): ?>
        <script>
            $(document).ready(function() {
                $('#petugas_datatable').DataTable({
                    "pageLength": 25,
                    "responsive": true,
                    "language": {
                        "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Indonesian.json"
                    },
                    "order": [
                        [0, "asc"]
                    ]
                });
            });
        </script>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp-8.2-new\htdocs\kapal\retrimarin_web\resources\views/petugas/index.blade.php ENDPATH**/ ?>