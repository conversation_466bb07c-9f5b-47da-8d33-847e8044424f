<?php $__env->startSection('title', 'Detail Retribusi - ' . config('app.name')); ?>

<?php $__env->startSection('content'); ?>
    <!-- Header -->
    <div class="card bg-light-info shadow-none position-relative overflow-hidden">
        <div class="card-body px-4 py-3">
            <div class="row align-items-center">
                <div class="col-9">
                    <h4 class="fw-semibold mb-8">Detail Retribusi</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a class="text-muted" href="<?php echo e(route('petugas.dashboard')); ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a class="text-muted" href="<?php echo e(route('petugas.retribusi.index')); ?>">Daftar Retribusi</a></li>
                            <li class="breadcrumb-item" aria-current="page">Detail</li>
                        </ol>
                    </nav>
                </div>
                <div class="col-3">
                    <div class="text-center mb-n5">
                        <img src="<?php echo e(asset('package/dist/images/breadcrumb/ChatBc.png')); ?>" alt=""
                            class="img-fluid mb-n4">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <h5 class="card-title fw-semibold mb-0">Informasi Retribusi</h5>
                        <div class="d-flex gap-2">
                            <?php if($retribusi->status_pembayaran == 'pending'): ?>
                                <form action="<?php echo e(route('petugas.retribusi.lunas', $retribusi->id)); ?>" method="POST" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('PATCH'); ?>
                                    <button type="submit" class="btn btn-success" 
                                            onclick="return confirm('Tandai retribusi ini sebagai lunas?')">
                                        <i class="ti ti-check me-2"></i>Tandai Lunas
                                    </button>
                                </form>
                                <form action="<?php echo e(route('petugas.retribusi.cancel', $retribusi->id)); ?>" method="POST" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('PATCH'); ?>
                                    <button type="submit" class="btn btn-danger" 
                                            onclick="return confirm('Batalkan retribusi ini?')">
                                        <i class="ti ti-x me-2"></i>Batalkan
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-semibold" width="40%">No. Retribusi:</td>
                                    <td><strong><?php echo e($retribusi->nomor_retribusi); ?></strong></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">Tanggal Transaksi:</td>
                                    <td><?php echo e($retribusi->tanggal_transaksi->format('d/m/Y H:i')); ?></td>
                                </tr>
                                <tr>
                                    <td class="fw-semibold">Status:</td>
                                    <td>
                                        <?php if($retribusi->status_pembayaran == 'pending'): ?>
                                            <span class="badge bg-warning rounded-3 fw-semibold">Pending</span>
                                        <?php elseif($retribusi->status_pembayaran == 'lunas'): ?>
                                            <span class="badge bg-success rounded-3 fw-semibold">Lunas</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger rounded-3 fw-semibold">Batal</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php if($retribusi->tanggal_pembayaran): ?>
                                    <tr>
                                        <td class="fw-semibold">Tanggal Pembayaran:</td>
                                        <td><?php echo e($retribusi->tanggal_pembayaran->format('d/m/Y H:i')); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td class="fw-semibold" width="40%">Jenis Pembayar:</td>
                                    <td>
                                        <span class="badge 
                                        <?php if($retribusi->jenis_pembayar == 'pribadi'): ?> bg-info <?php else: ?> bg-warning <?php endif; ?> rounded-3">
                                            <?php echo e($retribusi->jenis_pembayar_label); ?>

                                        </span>
                                    </td>
                                </tr>
                                <?php if($retribusi->jenis_pembayar == 'perusahaan' && $retribusi->company): ?>
                                    <tr>
                                        <td class="fw-semibold">Perusahaan:</td>
                                        <td><?php echo e($retribusi->company->nama_perusahaan); ?></td>
                                    </tr>
                                <?php endif; ?>
                                <tr>
                                    <td class="fw-semibold">Metode Pembayaran:</td>
                                    <td>
                                        <?php if($retribusi->metodePembayaran): ?>
                                            <?php echo e($retribusi->metodePembayaran->nama_metode); ?>

                                            <br><small class="text-muted"><?php echo e($retribusi->metodePembayaran->tipe_metode_label); ?></small>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Kapal Info -->
                    <div class="mt-4">
                        <h6 class="fw-semibold">Informasi Kapal</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-semibold" width="40%">Nama Kapal:</td>
                                        <td><?php echo e($retribusi->kapal->nama_kapal); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">Nomor IMO:</td>
                                        <td><?php echo e($retribusi->kapal->nomor_imo); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-semibold" width="40%">Jenis Kapal:</td>
                                        <td><?php echo e($retribusi->kapal->jenis_kapal); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">Bendera:</td>
                                        <td><?php echo e($retribusi->kapal->bendera); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Tarif Info -->
                    <div class="mt-4">
                        <h6 class="fw-semibold">Informasi Tarif</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-semibold" width="40%">Nama Tarif:</td>
                                        <td><?php echo e($retribusi->tarif->nama_tarif); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">Jenis Tarif:</td>
                                        <td><?php echo e($retribusi->tarif->jenis_tarif_label); ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td class="fw-semibold" width="40%">Harga Satuan:</td>
                                        <td><?php echo e($retribusi->tarif->formatted_harga); ?>/<?php echo e($retribusi->tarif->satuan); ?></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-semibold">Jumlah:</td>
                                        <td><?php echo e($retribusi->jumlah); ?> <?php echo e($retribusi->tarif->satuan); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <?php if($retribusi->keterangan): ?>
                        <div class="mt-4">
                            <h6 class="fw-semibold">Keterangan</h6>
                            <p class="text-muted"><?php echo e($retribusi->keterangan); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title fw-semibold">Ringkasan Pembayaran</h5>
                    <div class="text-center py-4">
                        <div class="mb-3">
                            <h3 class="fw-bold text-primary"><?php echo e($retribusi->formatted_total_harga); ?></h3>
                            <p class="text-muted mb-0">Total Retribusi</p>
                        </div>
                        <div class="mb-3">
                            <p class="mb-1"><?php echo e($retribusi->jumlah); ?> <?php echo e($retribusi->tarif->satuan); ?> × <?php echo e($retribusi->tarif->formatted_harga); ?></p>
                            <hr>
                            <h5 class="fw-bold"><?php echo e($retribusi->formatted_total_harga); ?></h5>
                        </div>
                    </div>
                </div>
            </div>

            <?php if($retribusi->metodePembayaran && $retribusi->metodePembayaran->tipe == 'qris' && $retribusi->metodePembayaran->foto_qris): ?>
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title fw-semibold">QRIS Pembayaran</h5>
                        <div class="text-center">
                            <img src="<?php echo e($retribusi->metodePembayaran->foto_qris_url); ?>" alt="QRIS" class="img-fluid" style="max-width: 250px;">
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <?php if($retribusi->metodePembayaran && $retribusi->metodePembayaran->tipe == 'transfer'): ?>
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title fw-semibold">Detail Transfer</h5>
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-semibold">Bank:</td>
                                <td><?php echo e($retribusi->metodePembayaran->nama_bank); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">No. Rekening:</td>
                                <td><?php echo e($retribusi->metodePembayaran->nomor_rekening); ?></td>
                            </tr>
                            <tr>
                                <td class="fw-semibold">Atas Nama:</td>
                                <td><?php echo e($retribusi->metodePembayaran->atas_nama); ?></td>
                            </tr>
                        </table>
                    </div>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-body">
                    <h5 class="card-title fw-semibold">Aksi Cepat</h5>
                    <div class="d-grid gap-2">
                        <?php if($retribusi->status_pembayaran == 'pending'): ?>
                            <form action="<?php echo e(route('petugas.retribusi.lunas', $retribusi->id)); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PATCH'); ?>
                                <button type="submit" class="btn btn-outline-success" 
                                        onclick="return confirm('Tandai retribusi ini sebagai lunas?')">
                                    <i class="ti ti-check me-2"></i>Tandai Lunas
                                </button>
                            </form>
                            <form action="<?php echo e(route('petugas.retribusi.cancel', $retribusi->id)); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PATCH'); ?>
                                <button type="submit" class="btn btn-outline-danger" 
                                        onclick="return confirm('Batalkan retribusi ini?')">
                                    <i class="ti ti-x me-2"></i>Batalkan
                                </button>
                            </form>
                        <?php endif; ?>
                        <a href="<?php echo e(route('petugas.retribusi.index')); ?>" class="btn btn-outline-primary">
                            <i class="ti ti-arrow-left me-2"></i>Kembali ke Daftar
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('petugas.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp-8.2-new\htdocs\kapal\retrimarin_web\resources\views/petugas/retribusi/show.blade.php ENDPATH**/ ?>